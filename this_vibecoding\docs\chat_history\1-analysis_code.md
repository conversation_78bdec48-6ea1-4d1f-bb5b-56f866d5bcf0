1. 本项目中集成了两个深度学习的子项目，请你根据规则 @0-parsecallchain.md ，以 @main.py 为起始代码，分析 LORE子项目@LORE-TSR 的整个调用链，分析结果保存到 @directory:this_vibecoding/docs/1-analysis_code目录下的readme_LORE_callchain.md中。

2. 本项目中集成了两个深度学习的子项目，请你根据规则 @0-parsecallchain.md ，以 @main.py 为起始代码，分析 LORE子项目@LORE-TSR 的整个调用链(需要详细到涉及的具体网络模块、损失函数、优化器、调度器等等)，分析结果保存到 @directory:this_vibecoding/docs/1-analysis_code目录下的readme_LORE_callchain.md中

3. 请整理上述关键核心内容并更新你的记忆库，以便后续快速了解 LORE-TSR的项目细节以及项目架构迁移

4. 请整理上述关键核心内容并更新你的记忆库，以便后续快速了解 LORE-TSR的项目细节以及项目架构迁移



------------------------------------------

4. 本项目中集成了两个深度学习的子项目（LORE-TSR和train-anything），请你根据规则 @d:\workspace\projects\TSRTransplantation/this_vibecoding\.windsurf\rules\0-parsecallchain.md， 
    1. 结合训练脚本： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\scripts\train\train_wireless_arcres.sh , 以 @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\main.py 为起始代码，结合项目的配置文件： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\lib\opts.py ；
    2. 分析 LORE子项目 @d:\workspace\projects\TSRTransplantation/LORE-TSR/ 的整个调用链；
    3. 并将分析结果保存到 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code/ 目录下的readme_LORE_callchain.md中。


5. 本项目中集成了两个深度学习的子项目（LORE-TSR和train-anything），请你根据规则 @d:\workspace\projects\TSRTransplantation/this_vibecoding\.windsurf\rules\0-parsecallchain.md， 
    1. 结合训练脚本： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\scripts\train\train_wireless_arcres.sh , 以 @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\main.py 为起始代码，结合项目的配置文件： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\lib\opts.py ；
    2. 分析 LORE子项目 @d:\workspace\projects\TSRTransplantation/LORE-TSR/ 的整个调用链；
    3. 并将分析结果保存到 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code/ 目录下的readme_LORE_callchain.md中。



6. 本项目中集成了两个深度学习的子项目（LORE-TSR和train-anything），请你根据规则 @d:\workspace\projects\TSRTransplantation/this_vibecoding\.windsurf\rules\0-parsecallchain.md，  
    1. 以 @d:\workspace\projects\TSRTransplantation/train-anything\training_loops\table_structure_recognition\train_cycle_centernet_ms.py 为起始代码，结合其对应的项目的配置文件： @d:\workspace\projects\TSRTransplantation/train-anything\configs\table_structure_recognition\cycle_centernet\cycle_centernet_ms_config.yaml ； 
    2. 分析 @d:\workspace\projects\TSRTransplantation/train-anything/ 子项目中的关于Cycle-CenterNet-MS的整个调用链； 
    3. 并将分析结果保存到 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code/ 目录下的readme_cycle-centernet-ms_callchain.md中。











    